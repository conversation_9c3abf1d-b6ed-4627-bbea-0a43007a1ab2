/**
 * Import/Export functionality for test cases and test suites
 */

class ImportExportManager {
    constructor() {
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Test Cases Export
        document.getElementById('exportTestCasesBtn')?.addEventListener('click', () => {
            this.exportTestCases();
        });

        // Test Cases Import
        document.getElementById('importTestCasesBtn')?.addEventListener('click', () => {
            this.showImportTestCasesModal();
        });

        document.getElementById('confirmImportTestCases')?.addEventListener('click', () => {
            this.importTestCases();
        });

        // Test Suites Export
        document.getElementById('exportTestSuitesBtn')?.addEventListener('click', () => {
            this.exportTestSuites();
        });

        // Test Suites Import
        document.getElementById('importTestSuitesBtn')?.addEventListener('click', () => {
            this.showImportTestSuitesModal();
        });

        document.getElementById('confirmImportTestSuites')?.addEventListener('click', () => {
            this.importTestSuites();
        });
    }

    async exportTestCases() {
        try {
            this.showLoadingState('exportTestCasesBtn', true);
            
            const response = await fetch('/api/test_cases/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                // Get the filename from the response headers or generate one
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = 'test_cases_export.zip';
                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (filenameMatch) {
                        filename = filenameMatch[1];
                    }
                }

                // Create blob and download
                const blob = await response.blob();
                this.downloadBlob(blob, filename);
                
                this.showNotification('Test cases exported successfully!', 'success');
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Export failed');
            }
        } catch (error) {
            console.error('Export error:', error);
            this.showNotification(`Export failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('exportTestCasesBtn', false);
        }
    }

    async exportTestSuites() {
        try {
            this.showLoadingState('exportTestSuitesBtn', true);
            
            const response = await fetch('/api/test_suites/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                // Get the filename from the response headers or generate one
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = 'test_suites_export.zip';
                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (filenameMatch) {
                        filename = filenameMatch[1];
                    }
                }

                // Create blob and download
                const blob = await response.blob();
                this.downloadBlob(blob, filename);
                
                this.showNotification('Test suites exported successfully!', 'success');
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Export failed');
            }
        } catch (error) {
            console.error('Export error:', error);
            this.showNotification(`Export failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('exportTestSuitesBtn', false);
        }
    }

    showImportTestCasesModal() {
        const modal = new bootstrap.Modal(document.getElementById('importTestCasesModal'));
        modal.show();
    }

    showImportTestSuitesModal() {
        const modal = new bootstrap.Modal(document.getElementById('importTestSuitesModal'));
        modal.show();
    }

    async importTestCases() {
        try {
            const fileInput = document.getElementById('testCasesFile');
            const conflictResolution = document.getElementById('testCasesConflictResolution').value;

            if (!fileInput.files || fileInput.files.length === 0) {
                this.showNotification('Please select a file to import', 'error');
                return;
            }

            const file = fileInput.files[0];
            if (!file.name.toLowerCase().endsWith('.zip')) {
                this.showNotification('Please select a ZIP file', 'error');
                return;
            }

            this.showLoadingState('confirmImportTestCases', true);

            const formData = new FormData();
            formData.append('file', file);
            formData.append('conflict_resolution', conflictResolution);

            const response = await fetch('/api/test_cases/import', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok && result.success !== false) {
                this.showImportResults('Test Cases', result);
                
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('importTestCasesModal'));
                modal.hide();
                
                // Refresh test cases list if available
                if (typeof loadTestCases === 'function') {
                    loadTestCases();
                }
            } else {
                throw new Error(result.error || 'Import failed');
            }
        } catch (error) {
            console.error('Import error:', error);
            this.showNotification(`Import failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('confirmImportTestCases', false);
        }
    }

    async importTestSuites() {
        try {
            const fileInput = document.getElementById('testSuitesFile');
            const conflictResolution = document.getElementById('testSuitesConflictResolution').value;

            if (!fileInput.files || fileInput.files.length === 0) {
                this.showNotification('Please select a file to import', 'error');
                return;
            }

            const file = fileInput.files[0];
            if (!file.name.toLowerCase().endsWith('.zip')) {
                this.showNotification('Please select a ZIP file', 'error');
                return;
            }

            this.showLoadingState('confirmImportTestSuites', true);

            const formData = new FormData();
            formData.append('file', file);
            formData.append('conflict_resolution', conflictResolution);

            const response = await fetch('/api/test_suites/import', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok && result.success !== false) {
                this.showImportResults('Test Suites', result);
                
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('importTestSuitesModal'));
                modal.hide();
                
                // Refresh test suites list if available
                if (typeof loadTestSuites === 'function') {
                    loadTestSuites();
                }
            } else {
                throw new Error(result.error || 'Import failed');
            }
        } catch (error) {
            console.error('Import error:', error);
            this.showNotification(`Import failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('confirmImportTestSuites', false);
        }
    }

    showImportResults(type, results) {
        let message = `${type} Import Results:\n`;
        message += `• Imported: ${results.imported || 0}\n`;
        message += `• Skipped: ${results.skipped || 0}\n`;
        message += `• Errors: ${results.errors || 0}`;
        
        if (results.conflicts && results.conflicts.length > 0) {
            message += `\n• Conflicts: ${results.conflicts.length}`;
        }
        
        if (results.error_details && results.error_details.length > 0) {
            message += `\n\nErrors:\n${results.error_details.join('\n')}`;
        }

        this.showNotification(message, results.errors > 0 ? 'warning' : 'success');
    }

    downloadBlob(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }

    showLoadingState(buttonId, loading) {
        const button = document.getElementById(buttonId);
        if (!button) return;

        if (loading) {
            button.disabled = true;
            const originalText = button.innerHTML;
            button.setAttribute('data-original-text', originalText);
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';
        } else {
            button.disabled = false;
            const originalText = button.getAttribute('data-original-text');
            if (originalText) {
                button.innerHTML = originalText;
                button.removeAttribute('data-original-text');
            }
        }
    }

    showNotification(message, type = 'info') {
        // Use existing notification system if available, otherwise use alert
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else if (typeof showToast === 'function') {
            showToast(message, type);
        } else {
            alert(message);
        }
    }
}

/**
 * Bulk Modification Manager for test cases
 */
class BulkModificationManager {
    constructor() {
        this.initializeEventListeners();
        this.previewData = null;
    }

    initializeEventListeners() {
        // Preview modifications
        document.getElementById('previewModificationsBtn')?.addEventListener('click', () => {
            this.previewModifications();
        });

        // Apply modifications
        document.getElementById('applyModificationsBtn')?.addEventListener('click', () => {
            this.applyModifications();
        });

        // Clear rules
        document.getElementById('clearRulesBtn')?.addEventListener('click', () => {
            this.clearRules();
        });
    }

    async previewModifications() {
        try {
            const rulesText = document.getElementById('modificationRules').value.trim();
            if (!rulesText) {
                this.showNotification('Please enter modification rules', 'error');
                return;
            }

            const rules = rulesText.split('\n').filter(rule => rule.trim() !== '');

            this.showLoadingState('previewModificationsBtn', true);

            const response = await fetch('/api/test_cases/bulk_modify/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ rules })
            });

            const result = await response.json();

            if (response.ok && result.status === 'success') {
                this.previewData = result.preview;
                this.displayPreviewResults(result.preview);

                // Enable apply button if there are modifications
                const applyBtn = document.getElementById('applyModificationsBtn');
                if (applyBtn) {
                    applyBtn.disabled = result.preview.affected_test_cases === 0;
                }
            } else {
                throw new Error(result.error || 'Preview failed');
            }
        } catch (error) {
            console.error('Preview error:', error);
            this.showNotification(`Preview failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('previewModificationsBtn', false);
        }
    }

    async applyModifications() {
        try {
            if (!this.previewData || this.previewData.affected_test_cases === 0) {
                this.showNotification('No modifications to apply. Please preview first.', 'error');
                return;
            }

            // Confirm with user
            const confirmMessage = `This will modify ${this.previewData.affected_test_cases} test case(s). Are you sure you want to continue?`;
            if (!confirm(confirmMessage)) {
                return;
            }

            const rulesText = document.getElementById('modificationRules').value.trim();
            const rules = rulesText.split('\n').filter(rule => rule.trim() !== '');
            const createBackup = document.getElementById('createBackup').checked;

            this.showLoadingState('applyModificationsBtn', true);

            const response = await fetch('/api/test_cases/bulk_modify/apply', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    rules,
                    create_backup: createBackup
                })
            });

            const result = await response.json();

            if (response.ok && result.status === 'success') {
                this.displayModificationResults(result.results);
                this.showNotification('Modifications applied successfully!', 'success');

                // Clear preview data and disable apply button
                this.previewData = null;
                document.getElementById('applyModificationsBtn').disabled = true;

                // Refresh test cases list if available
                if (typeof loadTestCases === 'function') {
                    loadTestCases();
                }
            } else {
                throw new Error(result.error || 'Modification failed');
            }
        } catch (error) {
            console.error('Modification error:', error);
            this.showNotification(`Modification failed: ${error.message}`, 'error');
        } finally {
            this.showLoadingState('applyModificationsBtn', false);
        }
    }

    clearRules() {
        document.getElementById('modificationRules').value = '';
        document.getElementById('previewResults').innerHTML = `
            <div class="text-muted text-center">
                <i class="bi bi-eye-slash"></i>
                <p>Click "Preview Changes" to see what modifications will be made</p>
            </div>
        `;
        document.getElementById('modificationResults').classList.add('d-none');
        document.getElementById('applyModificationsBtn').disabled = true;
        this.previewData = null;
    }

    displayPreviewResults(preview) {
        const container = document.getElementById('previewResults');

        if (preview.errors && preview.errors.length > 0) {
            let html = '<div class="alert alert-danger"><h6>Errors:</h6><ul class="mb-0">';
            preview.errors.forEach(error => {
                html += `<li>${this.escapeHtml(error)}</li>`;
            });
            html += '</ul></div>';
            container.innerHTML = html;
            return;
        }

        if (preview.affected_test_cases === 0) {
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> No test cases would be affected by these rules.
                    <br><small>Total test cases scanned: ${preview.total_test_cases}</small>
                </div>
            `;
            return;
        }

        let html = `
            <div class="alert alert-success">
                <h6><i class="bi bi-check-circle"></i> Preview Results:</h6>
                <ul class="mb-0">
                    <li>Total test cases: ${preview.total_test_cases}</li>
                    <li>Test cases to be modified: ${preview.affected_test_cases}</li>
                </ul>
            </div>
        `;

        if (preview.modifications && preview.modifications.length > 0) {
            html += '<div class="mt-3"><h6>Modifications by Test Case:</h6>';
            preview.modifications.forEach(mod => {
                html += `
                    <div class="card mb-2">
                        <div class="card-header py-2">
                            <strong>${this.escapeHtml(mod.test_case_name)}</strong>
                            <small class="text-muted">(${this.escapeHtml(mod.test_case)})</small>
                        </div>
                        <div class="card-body py-2">
                            <ul class="mb-0">
                `;
                mod.changes.forEach(change => {
                    html += `<li><small>${this.escapeHtml(change.description)}</small></li>`;
                });
                html += '</ul></div></div>';
            });
            html += '</div>';
        }

        container.innerHTML = html;
    }

    displayModificationResults(results) {
        const container = document.getElementById('resultsContent');
        const resultsDiv = document.getElementById('modificationResults');

        let html = `
            <div class="alert alert-${results.errors > 0 ? 'warning' : 'success'}">
                <h6><i class="bi bi-check-circle"></i> Modification Results:</h6>
                <ul class="mb-0">
                    <li>Total test cases processed: ${results.total_test_cases}</li>
                    <li>Test cases modified: ${results.modified_test_cases}</li>
                    ${results.backup_files.length > 0 ? `<li>Backup files created: ${results.backup_files.length}</li>` : ''}
                    ${results.errors > 0 ? `<li class="text-danger">Errors: ${results.errors}</li>` : ''}
                </ul>
            </div>
        `;

        if (results.errors && results.errors.length > 0) {
            html += '<div class="alert alert-danger mt-2"><h6>Errors:</h6><ul class="mb-0">';
            results.errors.forEach(error => {
                html += `<li><small>${this.escapeHtml(error)}</small></li>`;
            });
            html += '</ul></div>';
        }

        container.innerHTML = html;
        resultsDiv.classList.remove('d-none');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showLoadingState(buttonId, loading) {
        const button = document.getElementById(buttonId);
        if (!button) return;

        if (loading) {
            button.disabled = true;
            const originalText = button.innerHTML;
            button.setAttribute('data-original-text', originalText);
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';
        } else {
            button.disabled = false;
            const originalText = button.getAttribute('data-original-text');
            if (originalText) {
                button.innerHTML = originalText;
                button.removeAttribute('data-original-text');
            }
        }
    }

    showNotification(message, type = 'info') {
        // Use existing notification system if available, otherwise use alert
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else if (typeof showToast === 'function') {
            showToast(message, type);
        } else {
            alert(message);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.importExportManager = new ImportExportManager();
    window.bulkModificationManager = new BulkModificationManager();
});
